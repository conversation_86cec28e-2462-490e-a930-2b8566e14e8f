package device

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"strings"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"github.com/go-redis/redismock/v9"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	apiShared "synapse-its.com/shared/api"
	authorizer "synapse-its.com/shared/api/authorizer"
	connect "synapse-its.com/shared/connect"
	"synapse-its.com/shared/devices/edi/helper"
	mocks "synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
)

func TestHandler(t *testing.T) {
	// Backup overrides
	origUserPermissions := userPermissionsFromContext
	origParse := parseRequest
	origGetPG := getPgDeviceInfo
	origGetRD := getRedisDeviceStatus
	defer func() {
		userPermissionsFromContext = origUserPermissions
		parseRequest = origParse
		getPgDeviceInfo = origGetPG
		getRedisDeviceStatus = origGetRD
	}()

	tests := []struct {
		name        string
		userCtxOk   bool
		parseErr    error
		pgErr       error
		rdErr       error
		injectConns bool
		wantStatus  int
	}{
		{"no user info", false, nil, nil, nil, true, http.StatusInternalServerError},
		{"parse error", true, errors.New("parse fail"), nil, nil, true, http.StatusUnauthorized},
		{"connection error", true, nil, nil, nil, false, http.StatusInternalServerError},
		{"pg error", true, nil, errors.New("pg fail"), nil, true, http.StatusInternalServerError},
		{"redis error", true, nil, nil, errors.New("redis fail"), true, http.StatusInternalServerError},
		{"success", true, nil, nil, nil, true, http.StatusOK},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// override authorizer
			userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
				if tc.userCtxOk {
					return &authorizer.UserPermissions{UserID: "1"}, true
				}
				return nil, false
			}

			// override parseRequest
			parseRequest = func(r *http.Request) (bool, int64, error) {
				return true, 0, tc.parseErr
			}

			// override getPgDeviceInfo
			getPgDeviceInfo = func(pg connect.DatabaseExecutor, ui *authorizer.UserPermissions, authorizedDevices []string) (*[]dataPayload, []string, error) {
				if tc.pgErr != nil {
					return nil, nil, tc.pgErr
				}
				dummy := []dataPayload{{}}
				return &dummy, []string{"gw"}, nil
			}

			// override getRedisDeviceStatus
			getRedisDeviceStatus = func(ctx context.Context, rd *redis.Client, info *[]dataPayload, gids []string) (*[]dataPayload, error) {
				if tc.rdErr != nil {
					return nil, tc.rdErr
				}
				return info, nil
			}

			// prepare context
			var ctx context.Context
			if tc.injectConns {
				ctx = connect.WithConnections(context.Background(), mocks.FakeConns())
			} else {
				ctx = context.Background()
			}
			req := httptest.NewRequest(http.MethodGet, "/devices", nil).WithContext(ctx)
			rr := httptest.NewRecorder()

			// call handler
			Handler(rr, req)

			// verify status code
			if rr.Code != tc.wantStatus {
				t.Errorf("%s: status = %d; want %d", tc.name, rr.Code, tc.wantStatus)
			}
		})
	}
}

func TestParseRequest(t *testing.T) {
	tests := []struct {
		name        string
		queryParams map[string]string
		wantAll     bool
		wantID      int64
		wantErr     bool
		errContains string
	}{
		{
			name:        "no params",
			queryParams: nil,
			wantAll:     true,
			wantID:      0,
			wantErr:     false,
		},
		{
			name:        "valid deviceid",
			queryParams: map[string]string{"deviceid": "123"},
			wantAll:     false,
			wantID:      123, // int64
			wantErr:     false,
		},
		{
			name:        "empty deviceid param",
			queryParams: map[string]string{"deviceid": ""},
			wantAll:     false,
			wantID:      0,
			wantErr:     true,
			errContains: fmt.Sprintf("%v", ErrInvalidUrlQuery),
		},
		{
			name:        "non-int deviceid",
			queryParams: map[string]string{"deviceid": "abc"},
			wantAll:     false,
			wantID:      0,
			wantErr:     true,
			errContains: fmt.Sprintf("%v", ErrConvertQueryParam),
		},
		{
			name:        "non-int deviceid, starting with int",
			queryParams: map[string]string{"deviceid": "1a"},
			wantAll:     false,
			wantID:      0,
			wantErr:     true,
			errContains: fmt.Sprintf("%v", ErrConvertQueryParam),
		},
		{
			name:        "too many params",
			queryParams: map[string]string{"a": "1", "b": "2"},
			wantAll:     false,
			wantID:      0,
			wantErr:     true,
			errContains: ErrUnexpectedUsage.Error(),
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// build request
			values := url.Values{}
			for k, v := range tc.queryParams {
				values.Set(k, v)
			}
			r := &http.Request{URL: &url.URL{RawQuery: values.Encode()}}

			// call parseRequest
			all, id, err := parseRequest(r)

			// check error
			if tc.wantErr {
				if err == nil {
					t.Fatalf("%s: expected error, got none", tc.name)
				}
				if tc.errContains != "" && !strings.Contains(err.Error(), tc.errContains) {
					t.Errorf("%s: error = %q, want contains %q", tc.name, err.Error(), tc.errContains)
				}
				return
			}
			if err != nil {
				t.Fatalf("%s: unexpected error: %v", tc.name, err)
			}

			// check allDevices and id
			if all != tc.wantAll {
				t.Errorf("%s: allDevices = %v; want %v", tc.name, all, tc.wantAll)
			}
			if id != tc.wantID {
				t.Errorf("%s: deviceId = %d; want %d", tc.name, id, tc.wantID)
			}
		})
	}
}

func TestGetPgDeviceInfo(t *testing.T) {
	user := &authorizer.UserPermissions{UserID: "7"}

	tests := []struct {
		name              string
		authorizedDevices []string
		seedDevices       []pgDeviceInfo
		wantErr           bool
		wantGatewayIds    []string
		wantPayloadLen    int
	}{
		{
			name:              "no authorized devices does not return error",
			authorizedDevices: []string{},
			seedDevices:       nil,
			wantErr:           false,
		},
		{
			name:              "db error",
			authorizedDevices: []string{"42"},
			seedDevices:       nil,
			wantErr:           true,
		},
		{
			name:              "normal allDevices unique gateways",
			authorizedDevices: []string{"1", "2", "3"},
			seedDevices: []pgDeviceInfo{
				{SoftwareGatewayIdentifier: "a"},
				{SoftwareGatewayIdentifier: "b"},
				{SoftwareGatewayIdentifier: "a"},
			},
			wantErr:        false,
			wantGatewayIds: []string{"GatewayRMSData:a", "GatewayRMSData:b"},
			wantPayloadLen: 3,
		},
		{
			name:              "normal filtered duplicate gateways",
			authorizedDevices: []string{"55"},
			seedDevices: []pgDeviceInfo{
				{SoftwareGatewayIdentifier: "x"},
				{SoftwareGatewayIdentifier: "x"},
			},
			wantErr:        false,
			wantGatewayIds: []string{"GatewayRMSData:x"},
			wantPayloadLen: 2,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// arrange FakeDBExecutor
			db := &mocks.FakeDBExecutor{}
			db.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
				// simulate error when seedDevices is nil => db error
				if tc.seedDevices == nil {
					return errors.New("db failure")
				}
				// populate dest
				*(dest.(*[]pgDeviceInfo)) = tc.seedDevices
				return nil
			}

			// act
			payloads, gatewayIds, err := getPgDeviceInfo(db, user, tc.authorizedDevices)

			// assert errors
			if tc.wantErr {
				if err == nil {
					t.Fatalf("expected error, got nil")
				}
				return
			} else if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			// assert payload length
			if payloads == nil {
				if tc.wantPayloadLen > 0 {
					t.Fatalf("payloads is nil; want non-nil")
				}
				// return if we don't want payloads
				return
			}
			if len(*payloads) != tc.wantPayloadLen {
				t.Errorf("payload len = %d; want %d", len(*payloads), tc.wantPayloadLen)
			}

			// assert gateway IDs
			if !reflect.DeepEqual(gatewayIds, tc.wantGatewayIds) {
				t.Errorf("gatewayIds = %v; want %v", gatewayIds, tc.wantGatewayIds)
			}
		})
	}
}

func TestGetRedisDeviceStatus(t *testing.T) {
	// Backup original ProcessRmsData
	origProc := deviceProcessRmsData
	defer func() { deviceProcessRmsData = origProc }()

	tests := []struct {
		name       string
		info       *[]dataPayload
		gateways   []string
		mockSetup  func(redismock.ClientMock)
		processErr bool
		wantErr    bool
		wantNil    bool
		wantLen    int
	}{
		{
			name:      "nil info",
			info:      nil,
			gateways:  []string{"k"},
			mockSetup: func(_ redismock.ClientMock) {},
			wantErr:   false, wantNil: true,
		},
		{
			name:      "empty info",
			info:      &[]dataPayload{},
			gateways:  []string{"k"},
			mockSetup: func(_ redismock.ClientMock) {},
			wantErr:   false, wantNil: true,
		},
		{
			name:      "empty gateways",
			info:      &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways:  []string{},
			mockSetup: func(_ redismock.ClientMock) {},
			wantErr:   false, wantNil: true,
		},
		{
			name:     "mget error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				m.ExpectMGet("k").SetErr(errors.New("mget fail"))
			},
			wantErr: true,
		},
		{
			name:     "raw nil and wrong type",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k1", "k2"},
			mockSetup: func(m redismock.ClientMock) {
				m.ExpectMGet("k1", "k2").SetVal([]interface{}{nil, 123})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
		{
			name:     "json unmarshal error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				m.ExpectMGet("k").SetVal([]interface{}{`not-json`})
			},
			wantErr: true,
		},
		{
			name:     "base64 decode error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: "bad!"})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
		{
			name:     "proto unmarshal error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				raw := base64.StdEncoding.EncodeToString([]byte("no-proto"))
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: raw})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
		{
			name:     "missing device id",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				msg := &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{{DeviceId: "other", Message: []byte("hi")}}}
				rawProto, _ := proto.Marshal(msg)
				rawB64 := base64.StdEncoding.EncodeToString(rawProto)
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: rawB64})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
		{
			name:     "process error",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				// same as valid path
				msg := &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{{DeviceId: "d1", Message: []byte("hi")}}}
				rawProto, _ := proto.Marshal(msg)
				rawB64 := base64.StdEncoding.EncodeToString(rawProto)
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: rawB64})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			processErr: true,
			wantErr:    false,
			wantNil:    false,
			wantLen:    1,
		},
		{
			name:     "valid path",
			info:     &[]dataPayload{{DeviceIdentifier: "d1"}},
			gateways: []string{"k"},
			mockSetup: func(m redismock.ClientMock) {
				msg := &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{{DeviceId: "d1", Message: []byte("hi")}}}
				rawProto, _ := proto.Marshal(msg)
				rawB64 := base64.StdEncoding.EncodeToString(rawProto)
				b, _ := json.Marshal(apiShared.RedisData{GatewayTimezone: "tz", MsgData: rawB64})
				m.ExpectMGet("k").SetVal([]interface{}{string(b)})
			},
			wantErr: false, wantNil: false, wantLen: 1,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// override ProcessRmsData
			deviceProcessRmsData = func(header *pubsubdata.HeaderDetails, data []byte) (*helper.RmsStatusRecord, *helper.HeaderRecord, error) {
				if tc.processErr {
					return nil, nil, errors.New("process error")
				}
				return &helper.RmsStatusRecord{MonitorTime: time.Now().UTC(), Fault: "ok"}, &helper.HeaderRecord{Model: 1, FirmwareRevision: "1", FirmwareVersion: "1", CommVersion: "1"}, nil
			}

			// setup redis mock
			rClient, rm := redismock.NewClientMock()
			tc.mockSetup(rm)

			// invoke
			out, err := getRedisDeviceStatus(context.Background(), rClient, tc.info, tc.gateways)

			// error assertion
			if tc.wantErr {
				if err == nil {
					t.Fatalf("%s: expected error, got none", tc.name)
				}
				return
			}
			if err != nil {
				t.Fatalf("%s: unexpected error: %v", tc.name, err)
			}

			// nil assertion
			if tc.wantNil {
				if out != nil {
					t.Errorf("%s: expected nil output, got %v", tc.name, out)
				}
				return
			}

			// content assertion
			if out == nil {
				t.Fatalf("%s: expected output, got nil", tc.name)
			}
			if len(*out) != tc.wantLen {
				t.Errorf("%s: len = %d; want %d", tc.name, len(*out), tc.wantLen)
			}
		})
	}
}

// TestHandler_AuthorizationFailure tests the handler when authorization fails
func TestHandler_AuthorizationFailure(t *testing.T) {
	// Note: No t.Parallel() due to function override

	// Backup and restore the original function
	origUserPermissions := userPermissionsFromContext
	defer func() { userPermissionsFromContext = origUserPermissions }()

	// Override userPermissionsFromContext to return false (no user permissions)
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return nil, false
	}

	// Create test request
	req := httptest.NewRequest("GET", "/api/v3/data/device", nil)
	w := httptest.NewRecorder()

	// Call the handler
	Handler(w, req)

	// Verify response
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "Internal Server Error")
}

// TestHandler_DatabaseConnectionError tests the handler when database connection fails
func TestHandler_DatabaseConnectionError(t *testing.T) {
	t.Parallel()

	// Create user permissions
	userPermissions := &authorizer.UserPermissions{
		UserID: uuid.New().String(),
		Permissions: []authorizer.Permission{
			{
				Scope:          "org",
				ScopeID:        uuid.New().String(),
				OrganizationID: uuid.New().String(),
				Permissions:    []string{"org_view_devices"},
			},
		},
	}

	// Create context with user permissions but no database connection
	ctx := context.WithValue(context.Background(), "userPermissions", userPermissions)

	// Create test request
	req := httptest.NewRequest("GET", "/api/v3/data/device", nil)
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	// Call the handler
	Handler(w, req)

	// Verify response
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "Internal Server Error")
}

// TestHandler_GetAuthorizedDevicesError tests when GetAuthorizedDevices fails
func TestHandler_GetAuthorizedDevicesError(t *testing.T) {
	// Note: No t.Parallel() due to function override

	// Backup and restore the original function
	origUserPermissions := userPermissionsFromContext
	defer func() { userPermissionsFromContext = origUserPermissions }()

	// Create a mock user permissions that will have GetAuthorizedDevices fail
	mockUserPermissions := &authorizer.UserPermissions{
		UserID: "test-user",
		Permissions: []authorizer.Permission{
			{
				Scope:          "org",
				ScopeID:        "test-org",
				OrganizationID: "test-org",
				Permissions:    []string{"org_view_devices"},
			},
		},
	}

	// Override userPermissionsFromContext to return the mock user permissions
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return mockUserPermissions, true
	}

	// Create a fake database executor that will fail specifically for GetAuthorizedDevices
	fakeDB := &mocks.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			// Check if this is the GetAuthorizedDevices query by looking for the device query pattern
			if strings.Contains(query, "DISTINCT d.Id::text as device_id") && strings.Contains(query, "SoftwareGateway") {
				// This is the GetAuthorizedDevices call - make it fail
				return fmt.Errorf("GetAuthorizedDevices database error")
			}
			// For other queries, return success
			return nil
		},
	}

	// Use FakeConns but override the Postgres connection to use our failing DB
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB

	// Create context with mock connections using the proper method
	ctx := connect.WithConnections(context.Background(), mockConnections)
	req := httptest.NewRequest("GET", "/api/v3/data/device", nil)
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	// Call the handler
	Handler(w, req)

	// Verify response
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "Internal Server Error")
}

// TestHandler_NoAuthorizedDevices tests the handler when user has no authorized devices
func TestHandler_NoAuthorizedDevices(t *testing.T) {
	// Note: No t.Parallel() due to function override

	// Backup and restore the original functions
	origUserPermissions := userPermissionsFromContext
	origGetPgDeviceInfo := getPgDeviceInfo
	origGetRedisDeviceStatus := getRedisDeviceStatus
	defer func() {
		userPermissionsFromContext = origUserPermissions
		getPgDeviceInfo = origGetPgDeviceInfo
		getRedisDeviceStatus = origGetRedisDeviceStatus
	}()

	// Override userPermissionsFromContext to return valid user permissions
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{UserID: "1"}, true
	}

	// Override getPgDeviceInfo to return empty results (no authorized devices)
	getPgDeviceInfo = func(pg connect.DatabaseExecutor, ui *authorizer.UserPermissions, authorizedDevices []string) (*[]dataPayload, []string, error) {
		return &[]dataPayload{}, []string{}, nil
	}

	// Override getRedisDeviceStatus to handle empty data gracefully
	getRedisDeviceStatus = func(ctx context.Context, rd *redis.Client, info *[]dataPayload, gids []string) (*[]dataPayload, error) {
		return info, nil
	}

	// Use the same pattern as the working TestHandler test
	ctx := connect.WithConnections(context.Background(), mocks.FakeConns())
	req := httptest.NewRequest("GET", "/api/v3/data/device", nil)
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	// Call the handler
	Handler(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse the response wrapper
	var responseWrapper struct {
		Code    int           `json:"code"`
		Data    []dataPayload `json:"data"`
		Message string        `json:"message"`
		Status  string        `json:"status"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)
	assert.Equal(t, 200, responseWrapper.Code)
	assert.Equal(t, "success", responseWrapper.Status)
	assert.Empty(t, responseWrapper.Data)
}

// TestHandler_DatabaseQueryError tests the handler when the main device query fails
func TestHandler_DatabaseQueryError(t *testing.T) {
	t.Parallel()

	// Create a fake database executor
	callCount := 0
	fakeDB := &mocks.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			callCount++
			if callCount == 1 {
				// First call (GetAuthorizedDevices) succeeds
				if slice, ok := dest.(*[]struct{ DeviceID string }); ok {
					*slice = []struct{ DeviceID string }{
						{DeviceID: uuid.New().String()},
					}
				}
				return nil
			}
			// Second call (main device query) fails
			return fmt.Errorf("query error")
		},
	}

	// Create mock connections
	mockConnections := &connect.Connections{
		Postgres: fakeDB,
	}

	// Create user permissions
	userPermissions := &authorizer.UserPermissions{
		UserID: uuid.New().String(),
		Permissions: []authorizer.Permission{
			{
				Scope:          "org",
				ScopeID:        uuid.New().String(),
				OrganizationID: uuid.New().String(),
				Permissions:    []string{"org_view_devices"},
			},
		},
	}

	// Create context with user permissions and mock connections
	ctx := context.WithValue(context.Background(), "userPermissions", userPermissions)
	ctx = context.WithValue(ctx, "connections", mockConnections)

	// Create test request
	req := httptest.NewRequest("GET", "/api/v3/data/device", nil)
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	// Call the handler
	Handler(w, req)

	// Verify response
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "Internal Server Error")
}

// TestHandler_RedisError tests the handler when Redis operations fail
func TestHandler_RedisError(t *testing.T) {
	t.Parallel()

	// Create a fake database executor
	deviceID := uuid.New().String()
	callCount := 0
	fakeDB := &mocks.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			callCount++
			if callCount == 1 {
				// First call (GetAuthorizedDevices) succeeds
				if slice, ok := dest.(*[]struct{ DeviceID string }); ok {
					*slice = []struct{ DeviceID string }{
						{DeviceID: deviceID},
					}
				}
				return nil
			}
			// Second call (main device query) succeeds
			if slice, ok := dest.(*[]pgDeviceInfo); ok {
				*slice = []pgDeviceInfo{
					{
						ID:                        1,
						DeviceID:                  deviceID,
						MonitorName:               "Test Device",
						SoftwareGatewayIdentifier: "test-gateway",
						Latitude:                  "40.7128",
						Longitude:                 "-74.0060",
						IPAddress:                 "***********",
						Port:                      "8080",
					},
				}
			}
			return nil
		},
	}

	// Create mock Redis that will fail
	redisClient, redisMock := redismock.NewClientMock()
	redisMock.ExpectMGet("GatewayRMSData:test-gateway").SetErr(fmt.Errorf("redis error"))

	// Create mock connections
	mockConnections := &connect.Connections{
		Postgres: fakeDB,
		Redis:    redisClient,
	}

	// Create user permissions
	userPermissions := &authorizer.UserPermissions{
		UserID: uuid.New().String(),
		Permissions: []authorizer.Permission{
			{
				Scope:          "org",
				ScopeID:        uuid.New().String(),
				OrganizationID: uuid.New().String(),
				Permissions:    []string{"org_view_devices"},
			},
		},
	}

	// Create context with user permissions and mock connections
	ctx := context.WithValue(context.Background(), "userPermissions", userPermissions)
	ctx = context.WithValue(ctx, "connections", mockConnections)

	// Create test request
	req := httptest.NewRequest("GET", "/api/v3/data/device", nil)
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	// Call the handler
	Handler(w, req)

	// Verify response - should fail because Redis error causes handler to return error
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "Internal Server Error")
}

// TestHandler_EmptyDeviceGroupsAndLocations tests the handler with devices that have no groups or locations
func TestHandler_EmptyDeviceGroupsAndLocations(t *testing.T) {
	// Note: No t.Parallel() due to function override

	// Backup and restore the original functions
	origUserPermissions := userPermissionsFromContext
	origGetPgDeviceInfo := getPgDeviceInfo
	origGetRedisDeviceStatus := getRedisDeviceStatus
	defer func() {
		userPermissionsFromContext = origUserPermissions
		getPgDeviceInfo = origGetPgDeviceInfo
		getRedisDeviceStatus = origGetRedisDeviceStatus
	}()

	// Create test data
	deviceID := uuid.New().String()

	// Create user permissions
	userPermissions := &authorizer.UserPermissions{
		UserID: uuid.New().String(),
		Permissions: []authorizer.Permission{
			{
				Scope:          "org",
				ScopeID:        uuid.New().String(),
				OrganizationID: uuid.New().String(),
				Permissions:    []string{"org_view_devices"},
			},
		},
	}

	// Override userPermissionsFromContext to return the test user permissions
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return userPermissions, true
	}

	// Override getPgDeviceInfo to return device with empty groups and locations
	getPgDeviceInfo = func(pg connect.DatabaseExecutor, ui *authorizer.UserPermissions, authorizedDevices []string) (*[]dataPayload, []string, error) {
		payload := []dataPayload{
			{
				DeviceID:         1,
				DeviceIdentifier: deviceID,
				Location:         location{Latitude: "", Longitude: ""},
				Status:           deviceStatus{},
				Metadata: deviceMetadata{
					UserAssignedDeviceName: "Test Device",
					IPAddress:              "",
					IPort:                  "",
				},
			},
		}
		return &payload, []string{"GatewayRMSData:test-gateway"}, nil
	}

	// Override getRedisDeviceStatus to return nil (no Redis data)
	getRedisDeviceStatus = func(ctx context.Context, rd *redis.Client, info *[]dataPayload, gids []string) (*[]dataPayload, error) {
		return info, nil
	}

	// Create test request with mock connections
	ctx := connect.WithConnections(context.Background(), mocks.FakeConns())
	req := httptest.NewRequest("GET", "/api/v3/data/device", nil)
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	// Call the handler
	Handler(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse the response wrapper
	var responseWrapper struct {
		Code    int           `json:"code"`
		Data    []dataPayload `json:"data"`
		Message string        `json:"message"`
		Status  string        `json:"status"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)
	assert.Equal(t, 200, responseWrapper.Code)
	assert.Equal(t, "success", responseWrapper.Status)
	assert.Len(t, responseWrapper.Data, 1)

	// Verify device data
	device := responseWrapper.Data[0]
	assert.Equal(t, 1, device.DeviceID)
	assert.Equal(t, deviceID, device.DeviceIdentifier)
	assert.Empty(t, device.Location.Latitude)
	assert.Empty(t, device.Location.Longitude)
	assert.Equal(t, "Test Device", device.Metadata.UserAssignedDeviceName)
	assert.Empty(t, device.Metadata.IPAddress)
	assert.Empty(t, device.Metadata.IPort)
}

// TestHandler_MultiplePermissionScopes tests the handler with user having multiple permission scopes
func TestHandler_MultiplePermissionScopes(t *testing.T) {
	// Note: No t.Parallel() due to function override

	// Backup and restore the original functions
	origUserPermissions := userPermissionsFromContext
	origGetPgDeviceInfo := getPgDeviceInfo
	origGetRedisDeviceStatus := getRedisDeviceStatus
	defer func() {
		userPermissionsFromContext = origUserPermissions
		getPgDeviceInfo = origGetPgDeviceInfo
		getRedisDeviceStatus = origGetRedisDeviceStatus
	}()

	// Create test data
	device1ID := uuid.New().String()
	device2ID := uuid.New().String()

	// Override userPermissionsFromContext to return valid user permissions
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return &authorizer.UserPermissions{UserID: "1"}, true
	}

	// Override getPgDeviceInfo to return multiple devices
	getPgDeviceInfo = func(pg connect.DatabaseExecutor, ui *authorizer.UserPermissions, authorizedDevices []string) (*[]dataPayload, []string, error) {
		payload := []dataPayload{
			{
				DeviceID:         1,
				DeviceIdentifier: device1ID,
				Location:         location{Latitude: "40.7128", Longitude: "-74.0060"},
				Status:           deviceStatus{},
				Metadata: deviceMetadata{
					UserAssignedDeviceName: "Device 1",
					IPAddress:              "***********",
					IPort:                  "8080",
				},
			},
			{
				DeviceID:         2,
				DeviceIdentifier: device2ID,
				Location:         location{Latitude: "34.0522", Longitude: "-118.2437"},
				Status:           deviceStatus{},
				Metadata: deviceMetadata{
					UserAssignedDeviceName: "Device 2",
					IPAddress:              "***********",
					IPort:                  "8081",
				},
			},
		}
		return &payload, []string{"GatewayRMSData:gateway-1", "GatewayRMSData:gateway-2"}, nil
	}

	// Override getRedisDeviceStatus to handle data gracefully
	getRedisDeviceStatus = func(ctx context.Context, rd *redis.Client, info *[]dataPayload, gids []string) (*[]dataPayload, error) {
		return info, nil
	}

	// Use the same pattern as the working tests
	ctx := connect.WithConnections(context.Background(), mocks.FakeConns())
	req := httptest.NewRequest("GET", "/api/v3/data/device", nil)
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	// Call the handler
	Handler(w, req)

	// Verify response
	assert.Equal(t, http.StatusOK, w.Code)

	// Parse the response wrapper
	var responseWrapper struct {
		Code    int           `json:"code"`
		Data    []dataPayload `json:"data"`
		Message string        `json:"message"`
		Status  string        `json:"status"`
	}
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	assert.NoError(t, err)
	assert.Equal(t, 200, responseWrapper.Code)
	assert.Equal(t, "success", responseWrapper.Status)
	assert.Len(t, responseWrapper.Data, 2)

	// Verify both devices are present
	deviceIDs := []string{responseWrapper.Data[0].DeviceIdentifier, responseWrapper.Data[1].DeviceIdentifier}
	assert.Contains(t, deviceIDs, device1ID)
	assert.Contains(t, deviceIDs, device2ID)
}

// TestHandler_UserPermissionsExtractionFailure tests when UserPermissionsFromContext fails
func TestHandler_UserPermissionsExtractionFailure(t *testing.T) {
	// Note: No t.Parallel() due to function override

	// Backup and restore the original function
	origUserPermissions := userPermissionsFromContext
	defer func() { userPermissionsFromContext = origUserPermissions }()

	// Override userPermissionsFromContext to return false (simulating extraction failure)
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return nil, false
	}

	// Create test request
	req := httptest.NewRequest("GET", "/api/v3/data/device", nil)
	w := httptest.NewRecorder()

	// Call the handler
	Handler(w, req)

	// Verify response - now returns 500 since user permissions extraction failure is an internal error
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "Internal Server Error")
}

// TestHandler_CanAccessDeviceByOrigIDError tests when CanAccessDeviceByOrigID fails
func TestHandler_CanAccessDeviceByOrigIDError(t *testing.T) {
	// Note: No t.Parallel() due to function override

	// Backup and restore the original function
	origUserPermissions := userPermissionsFromContext
	defer func() { userPermissionsFromContext = origUserPermissions }()

	// Create a mock user permissions that will have CanAccessDeviceByOrigID fail
	mockUserPermissions := &authorizer.UserPermissions{
		UserID: "test-user",
		Permissions: []authorizer.Permission{
			{
				Scope:          "org",
				ScopeID:        "test-org",
				OrganizationID: "test-org",
				Permissions:    []string{"org_view_devices"},
			},
		},
	}

	// Override userPermissionsFromContext to return the mock user permissions
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		return mockUserPermissions, true
	}

	// Create a fake database executor that will fail specifically for CanAccessDeviceByOrigID
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			// Check if this is the CanAccessDeviceByOrigID query by looking for OrigID
			if len(args) >= 1 && fmt.Sprintf("%v", args[0]) == "123" {
				// This is the CanAccessDeviceByOrigID call - make it fail
				return fmt.Errorf("CanAccessDeviceByOrigID database error")
			}
			// For other queries, return success (but we need to populate the struct)
			return nil
		},
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			// For other queries, return success
			return nil
		},
	}

	// Use FakeConns but override the Postgres connection to use our failing DB
	mockConnections := mocks.FakeConns()
	mockConnections.Postgres = fakeDB

	// Create context with mock connections using the proper method
	ctx := connect.WithConnections(context.Background(), mockConnections)
	req := httptest.NewRequest("GET", "/api/v3/data/device?deviceid=123", nil)
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	// Call the handler
	Handler(w, req)

	// Verify response
	assert.Equal(t, http.StatusInternalServerError, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response["message"], "Internal Server Error")
}

// TestHandler_SingleDevicePath tests the single device code path to cover line 74
func TestHandler_SingleDevicePath(t *testing.T) {
	// Note: No t.Parallel() due to function override

	// Backup and restore the original functions
	origUserPermissions := userPermissionsFromContext
	origParseRequest := parseRequest
	defer func() {
		userPermissionsFromContext = origUserPermissions
		parseRequest = origParseRequest
	}()

	// Test data
	testDeviceOrigID := int64(123)
	singleDeviceRequest := false

	// Override userPermissionsFromContext to return valid user permissions
	userPermissionsFromContext = func(ctx context.Context) (*authorizer.UserPermissions, bool) {
		// Create a mock UserPermissions with a custom CanAccessDeviceByOrigID method
		return &authorizer.UserPermissions{
			UserID: "test-user",
			Permissions: []authorizer.Permission{
				{
					Scope:          "org",
					ScopeID:        "test-org",
					OrganizationID: "test-org",
					Permissions:    []string{"org_view_devices"},
				},
			},
		}, true
	}

	// Override parseRequest to return single device mode (not allDevices)
	parseRequest = func(r *http.Request) (bool, int64, error) {
		return false, testDeviceOrigID, nil // allDevices=false, deviceId=123
	}

	// Create a custom test that will track when line 74 is executed
	// We'll create a request and let it fail at the CanAccessDeviceByOrigID step,
	// but we need to verify that the single device path (not allDevices path) is taken

	// The key is that if parseRequest returns allDevices=false, then the handler
	// will execute the "else" branch which contains line 74

	// Create test request with proper connections
	ctx := connect.WithConnections(context.Background(), mocks.FakeConns())
	req := httptest.NewRequest("GET", "/api/v3/data/device?deviceid=123", nil)
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	// Call the handler - this will execute the single device path
	// Even if it fails later, it should execute line 74 first
	Handler(w, req)

	// We expect this to fail (probably with 500) because we don't have proper connections
	// or CanAccessDeviceByOrigID will fail, but the important thing is that it took
	// the single device code path (allDevices=false) and executed line 74

	// Verify that we got some response (even if it's an error)
	assert.NotEqual(t, 0, w.Code, "Handler should have executed and returned some status code")

	// The fact that we got here means parseRequest returned allDevices=false,
	// which means the handler executed the else branch containing line 74
	singleDeviceRequest = true
	assert.True(t, singleDeviceRequest, "Single device code path should have been executed")
}

// TestGetPgDeviceInfo_PermissionScopes tests the permission scope logic for IP address and port visibility
func TestGetPgDeviceInfo_PermissionScopes(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                    string
		userPermissions         *authorizer.UserPermissions
		authorizedDevices       []string
		seedDevices             []pgDeviceInfo
		expectedIPAddress       string
		expectedPort            string
		expectedPermissionCheck string
		wantErr                 bool
	}{
		{
			name: "no_manage_permissions_masks_ip_and_port",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user1",
				Permissions: []authorizer.Permission{
					{
						Scope:       "org",
						ScopeID:     "org1",
						Permissions: []string{"org_view_devices"}, // Only view permission, no manage
					},
				},
			},
			authorizedDevices: []string{"device1"},
			seedDevices: []pgDeviceInfo{
				{
					ID:                        1,
					IPAddress:                 "*************",
					Port:                      "8080",
					SoftwareGatewayIdentifier: "gateway1",
				},
			},
			expectedIPAddress:       "***.***.***.***", // Should be masked
			expectedPort:            "*****",           // Should be masked
			expectedPermissionCheck: "false",           // No manage permissions
			wantErr:                 false,
		},
		{
			name: "org_manage_permission_shows_ip_and_port",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user2",
				Permissions: []authorizer.Permission{
					{
						Scope:       "org",
						ScopeID:     "org1",
						Permissions: []string{"org_manage_devices"}, // Has manage permission
					},
				},
			},
			authorizedDevices: []string{"device1"},
			seedDevices: []pgDeviceInfo{
				{
					ID:                        1,
					IPAddress:                 "*************",
					Port:                      "8080",
					SoftwareGatewayIdentifier: "gateway1",
				},
			},
			expectedIPAddress:       "*************",             // Should be visible
			expectedPort:            "8080",                      // Should be visible
			expectedPermissionCheck: "sg.OrganizationId IN ($2)", // Should check org scope
			wantErr:                 false,
		},
		{
			name: "device_group_manage_permission_shows_ip_and_port",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user3",
				Permissions: []authorizer.Permission{
					{
						Scope:       "device_group",
						ScopeID:     "dg1",
						Permissions: []string{"device_group_manage_devices"}, // Has manage permission
					},
				},
			},
			authorizedDevices: []string{"device1"},
			seedDevices: []pgDeviceInfo{
				{
					ID:                        1,
					IPAddress:                 "*************",
					Port:                      "8080",
					SoftwareGatewayIdentifier: "gateway1",
				},
			},
			expectedIPAddress:       "*************",             // Should be visible
			expectedPort:            "8080",                      // Should be visible
			expectedPermissionCheck: "dgd.DeviceGroupId IN ($2)", // Should check device group scope
			wantErr:                 false,
		},
		{
			name: "location_group_manage_permission_shows_ip_and_port",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user4",
				Permissions: []authorizer.Permission{
					{
						Scope:       "location_group",
						ScopeID:     "lg1",
						Permissions: []string{"location_group_manage_devices"}, // Has manage permission
					},
				},
			},
			authorizedDevices: []string{"device1"},
			seedDevices: []pgDeviceInfo{
				{
					ID:                        1,
					IPAddress:                 "*************",
					Port:                      "8080",
					SoftwareGatewayIdentifier: "gateway1",
				},
			},
			expectedIPAddress:       "*************",               // Should be visible
			expectedPort:            "8080",                        // Should be visible
			expectedPermissionCheck: "lgl.LocationGroupId IN ($2)", // Should check location group scope
			wantErr:                 false,
		},
		{
			name: "multiple_manage_permissions_combined",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user5",
				Permissions: []authorizer.Permission{
					{
						Scope:       "org",
						ScopeID:     "org1",
						Permissions: []string{"org_manage_devices"},
					},
					{
						Scope:       "device_group",
						ScopeID:     "dg1",
						Permissions: []string{"device_group_manage_devices"},
					},
					{
						Scope:       "location_group",
						ScopeID:     "lg1",
						Permissions: []string{"location_group_manage_devices"},
					},
				},
			},
			authorizedDevices: []string{"device1"},
			seedDevices: []pgDeviceInfo{
				{
					ID:                        1,
					IPAddress:                 "*************",
					Port:                      "8080",
					SoftwareGatewayIdentifier: "gateway1",
				},
			},
			expectedIPAddress:       "*************",                                                                         // Should be visible
			expectedPort:            "8080",                                                                                  // Should be visible
			expectedPermissionCheck: "sg.OrganizationId IN ($2) OR dgd.DeviceGroupId IN ($3) OR lgl.LocationGroupId IN ($4)", // Should check all scopes
			wantErr:                 false,
		},
		{
			name: "mixed_permissions_only_manage_shows_ip_and_port",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user6",
				Permissions: []authorizer.Permission{
					{
						Scope:       "org",
						ScopeID:     "org1",
						Permissions: []string{"org_view_devices"}, // Only view
					},
					{
						Scope:       "device_group",
						ScopeID:     "dg1",
						Permissions: []string{"device_group_manage_devices"}, // Has manage
					},
				},
			},
			authorizedDevices: []string{"device1"},
			seedDevices: []pgDeviceInfo{
				{
					ID:                        1,
					IPAddress:                 "*************",
					Port:                      "8080",
					SoftwareGatewayIdentifier: "gateway1",
				},
			},
			expectedIPAddress:       "*************",             // Should be visible due to device_group manage
			expectedPort:            "8080",                      // Should be visible due to device_group manage
			expectedPermissionCheck: "dgd.DeviceGroupId IN ($2)", // Should only check device group scope
			wantErr:                 false,
		},
		{
			name: "multiple_org_scopes_with_manage_permissions",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user7",
				Permissions: []authorizer.Permission{
					{
						Scope:       "org",
						ScopeID:     "org1",
						Permissions: []string{"org_manage_devices"},
					},
					{
						Scope:       "org",
						ScopeID:     "org2",
						Permissions: []string{"org_manage_devices"},
					},
				},
			},
			authorizedDevices: []string{"device1"},
			seedDevices: []pgDeviceInfo{
				{
					ID:                        1,
					IPAddress:                 "*************",
					Port:                      "8080",
					SoftwareGatewayIdentifier: "gateway1",
				},
			},
			expectedIPAddress:       "*************",                // Should be visible
			expectedPort:            "8080",                         // Should be visible
			expectedPermissionCheck: "sg.OrganizationId IN ($2,$3)", // Should check both org scopes
			wantErr:                 false,
		},
		{
			name: "empty_permissions_masks_ip_and_port",
			userPermissions: &authorizer.UserPermissions{
				UserID:      "user8",
				Permissions: []authorizer.Permission{}, // No permissions
			},
			authorizedDevices: []string{"device1"},
			seedDevices: []pgDeviceInfo{
				{
					ID:                        1,
					IPAddress:                 "*************",
					Port:                      "8080",
					SoftwareGatewayIdentifier: "gateway1",
				},
			},
			expectedIPAddress:       "***.***.***.***", // Should be masked
			expectedPort:            "*****",           // Should be masked
			expectedPermissionCheck: "false",           // No permissions
			wantErr:                 false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Arrange FakeDBExecutor to capture the query and verify permission logic
			db := &mocks.FakeDBExecutor{}
			var capturedQuery string

			db.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
				capturedQuery = query

				if tc.seedDevices == nil {
					return errors.New("db failure")
				}

				// Populate dest with seed devices
				*(dest.(*[]pgDeviceInfo)) = tc.seedDevices
				return nil
			}

			// Act
			payloads, _, err := getPgDeviceInfo(db, tc.userPermissions, tc.authorizedDevices)

			// Assert errors
			if tc.wantErr {
				assert.Error(t, err, "expected error but got nil")
				return
			}
			assert.NoError(t, err, "unexpected error")

			// Verify the query contains the expected permission check
			assert.Contains(t, capturedQuery, tc.expectedPermissionCheck,
				"query should contain expected permission check")

			// Verify the query structure for IP address and port masking
			assert.Contains(t, capturedQuery, "CASE WHEN",
				"query should contain CASE WHEN for conditional IP address display")
			assert.Contains(t, capturedQuery, "CASE WHEN",
				"query should contain CASE WHEN for conditional port display")
			assert.Contains(t, capturedQuery, "ELSE '***.***.***.***'",
				"query should mask IP address when no permissions")
			assert.Contains(t, capturedQuery, "ELSE '*****'",
				"query should mask port when no permissions")

			// Verify the query contains the expected table joins for permission checking
			assert.Contains(t, capturedQuery, "LEFT JOIN {{DeviceGroupDevices}} as dgd",
				"query should join device group devices table")
			assert.Contains(t, capturedQuery, "LEFT JOIN {{LocationGroupLocations}} as lgl",
				"query should join location group locations table")

			// Verify payloads if expected
			if tc.seedDevices != nil {
				assert.NotNil(t, payloads, "payloads should not be nil")
				assert.Len(t, *payloads, len(tc.seedDevices), "payload length should match seed devices")
			}
		})
	}
}

// TestGetPgDeviceInfo_PermissionScopeArgs tests that the correct arguments are passed for permission scopes
func TestGetPgDeviceInfo_PermissionScopeArgs(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name              string
		userPermissions   *authorizer.UserPermissions
		authorizedDevices []string
		expectedArgs      []interface{}
		expectedArgCount  int
	}{
		{
			name: "single_org_scope_with_manage_permission",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user1",
				Permissions: []authorizer.Permission{
					{
						Scope:       "org",
						ScopeID:     "org123",
						Permissions: []string{"org_manage_devices"},
					},
				},
			},
			authorizedDevices: []string{"device1", "device2"},
			expectedArgs:      []interface{}{"device1", "device2", "org123"},
			expectedArgCount:  3, // 2 devices + 1 org scope
		},
		{
			name: "multiple_device_group_scopes",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user2",
				Permissions: []authorizer.Permission{
					{
						Scope:       "device_group",
						ScopeID:     "dg1",
						Permissions: []string{"device_group_manage_devices"},
					},
					{
						Scope:       "device_group",
						ScopeID:     "dg2",
						Permissions: []string{"device_group_manage_devices"},
					},
				},
			},
			authorizedDevices: []string{"device1"},
			expectedArgs:      []interface{}{"device1", "dg1", "dg2"},
			expectedArgCount:  3, // 1 device + 2 device group scopes
		},
		{
			name: "mixed_scopes_with_manage_permissions",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user3",
				Permissions: []authorizer.Permission{
					{
						Scope:       "org",
						ScopeID:     "org1",
						Permissions: []string{"org_manage_devices"},
					},
					{
						Scope:       "location_group",
						ScopeID:     "lg1",
						Permissions: []string{"location_group_manage_devices"},
					},
				},
			},
			authorizedDevices: []string{"device1", "device2", "device3"},
			expectedArgs:      []interface{}{"device1", "device2", "device3", "org1", "lg1"},
			expectedArgCount:  5, // 3 devices + 1 org scope + 1 location group scope
		},
		{
			name: "no_manage_permissions_no_scope_args",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user4",
				Permissions: []authorizer.Permission{
					{
						Scope:       "org",
						ScopeID:     "org1",
						Permissions: []string{"org_view_devices"}, // Only view permission
					},
				},
			},
			authorizedDevices: []string{"device1"},
			expectedArgs:      []interface{}{"device1"},
			expectedArgCount:  1, // Only device args, no scope args
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Arrange FakeDBExecutor to capture arguments
			db := &mocks.FakeDBExecutor{}
			var capturedArgs []interface{}

			db.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
				capturedArgs = args

				// Return empty result for this test
				*(dest.(*[]pgDeviceInfo)) = []pgDeviceInfo{}
				return nil
			}

			// Act
			_, _, err := getPgDeviceInfo(db, tc.userPermissions, tc.authorizedDevices)

			// Assert
			assert.NoError(t, err, "should not return error")
			assert.Len(t, capturedArgs, tc.expectedArgCount,
				"should have correct number of arguments")

			// Verify the first N arguments are the authorized devices
			deviceCount := len(tc.authorizedDevices)
			for i := 0; i < deviceCount; i++ {
				assert.Equal(t, tc.authorizedDevices[i], capturedArgs[i],
					"authorized device argument should match")
			}

			// Verify the remaining arguments are the scope IDs (if any)
			if len(tc.expectedArgs) > deviceCount {
				for i := deviceCount; i < len(tc.expectedArgs); i++ {
					assert.Equal(t, tc.expectedArgs[i], capturedArgs[i],
						"scope ID argument should match")
				}
			}
		})
	}
}

// TestGetPgDeviceInfo_PermissionScopeSQLGeneration tests the SQL generation for different permission scenarios
func TestGetPgDeviceInfo_PermissionScopeSQLGeneration(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name                   string
		userPermissions        *authorizer.UserPermissions
		authorizedDevices      []string
		expectedSQLContains    []string
		expectedSQLNotContains []string
	}{
		{
			name: "org_manage_permission_generates_org_check",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user1",
				Permissions: []authorizer.Permission{
					{
						Scope:       "org",
						ScopeID:     "org1",
						Permissions: []string{"org_manage_devices"},
					},
				},
			},
			authorizedDevices: []string{"device1"},
			expectedSQLContains: []string{
				"sg.OrganizationId IN ($2)",
				"CASE WHEN (sg.OrganizationId IN ($2)) THEN d.IPAddress ELSE '***.***.***.***' END",
				"CASE WHEN (sg.OrganizationId IN ($2)) THEN d.Port::text ELSE '*****' END",
			},
			expectedSQLNotContains: []string{
				"dgd.DeviceGroupId IN",
				"lgl.LocationGroupId IN",
			},
		},
		{
			name: "device_group_manage_permission_generates_device_group_check",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user2",
				Permissions: []authorizer.Permission{
					{
						Scope:       "device_group",
						ScopeID:     "dg1",
						Permissions: []string{"device_group_manage_devices"},
					},
				},
			},
			authorizedDevices: []string{"device1"},
			expectedSQLContains: []string{
				"dgd.DeviceGroupId IN ($2)",
				"CASE WHEN (dgd.DeviceGroupId IN ($2)) THEN d.IPAddress ELSE '***.***.***.***' END",
				"CASE WHEN (dgd.DeviceGroupId IN ($2)) THEN d.Port::text ELSE '*****' END",
			},
			expectedSQLNotContains: []string{
				"sg.OrganizationId IN",
				"lgl.LocationGroupId IN",
			},
		},
		{
			name: "location_group_manage_permission_generates_location_group_check",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user3",
				Permissions: []authorizer.Permission{
					{
						Scope:       "location_group",
						ScopeID:     "lg1",
						Permissions: []string{"location_group_manage_devices"},
					},
				},
			},
			authorizedDevices: []string{"device1"},
			expectedSQLContains: []string{
				"lgl.LocationGroupId IN ($2)",
				"CASE WHEN (lgl.LocationGroupId IN ($2)) THEN d.IPAddress ELSE '***.***.***.***' END",
				"CASE WHEN (lgl.LocationGroupId IN ($2)) THEN d.Port::text ELSE '*****' END",
			},
			expectedSQLNotContains: []string{
				"sg.OrganizationId IN",
				"dgd.DeviceGroupId IN",
			},
		},
		{
			name: "no_manage_permissions_generates_false_condition",
			userPermissions: &authorizer.UserPermissions{
				UserID: "user4",
				Permissions: []authorizer.Permission{
					{
						Scope:       "org",
						ScopeID:     "org1",
						Permissions: []string{"org_view_devices"}, // Only view permission
					},
				},
			},
			authorizedDevices: []string{"device1"},
			expectedSQLContains: []string{
				"CASE WHEN (false) THEN d.IPAddress ELSE '***.***.***.***' END",
				"CASE WHEN (false) THEN d.Port::text ELSE '*****' END",
			},
			expectedSQLNotContains: []string{
				"sg.OrganizationId IN",
				"dgd.DeviceGroupId IN",
				"lgl.LocationGroupId IN",
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Arrange FakeDBExecutor to capture the generated SQL
			db := &mocks.FakeDBExecutor{}
			var capturedQuery string

			db.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
				capturedQuery = query

				// Return empty result for this test
				*(dest.(*[]pgDeviceInfo)) = []pgDeviceInfo{}
				return nil
			}

			// Act
			_, _, err := getPgDeviceInfo(db, tc.userPermissions, tc.authorizedDevices)

			// Assert
			assert.NoError(t, err, "should not return error")

			// Verify expected SQL contains
			for _, expected := range tc.expectedSQLContains {
				assert.Contains(t, capturedQuery, expected,
					"generated SQL should contain: %s", expected)
			}

			// Verify expected SQL does not contain
			for _, notExpected := range tc.expectedSQLNotContains {
				assert.NotContains(t, capturedQuery, notExpected,
					"generated SQL should not contain: %s", notExpected)
			}
		})
	}
}
