package rest

import (
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/testing/utils"
)

func Test_OnrampUserPermission_Success(t *testing.T) {
	assert := assert.New(t)
	ctx := t.Context()

	// Set up database connections
	connections := connect.NewConnections(ctx)
	pg := connections.Postgres

	// Get user permissions for the specified user ID
	userPermissions, err := authorizer.GetUserPermissions(pg, "45627c04-8d87-595a-a31b-2e675e22417a")
	logger.Debugf("userPermissions UserID: %+v", userPermissions.UserID)
	logger.Debugf("userPermissions: %+v", userPermissions)
	assert.NoError(err)
	assert.NotNil(userPermissions)
	assert.Equal(userPermissions.UserID, "45627c04-8d87-595a-a31b-2e675e22417a")

	// Validate that we have at least one permission
	assert.Greater(len(userPermissions.Permissions), 0, "User should have at least one permission")

	// Log the actual permissions for debugging
	for i, perm := range userPermissions.Permissions {
		logger.Debugf("Permission %d: Scope=%s, ScopeID=%s, OrgID=%s, Permissions=%v",
			i, perm.Scope, perm.ScopeID, perm.OrganizationID, perm.Permissions)
	}

	// Wait for onramp service to be ready
	assert.NoError(utils.AwaitOnramp(ctx, 30*time.Second), "onramp service should be ready")

	// Call callback endpoint to pass userPermissions to the callback endpoint
	callbackURL := "http://localhost:8090/callback"

