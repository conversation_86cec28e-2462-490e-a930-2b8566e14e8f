package assets

import (
	_ "embed"
	"net/http"
	"os"
	"strings"
)

//go:embed env.template.js
var envJsTemplate string

// Cached version of the env.js file with environment variables
// substituted at runtime.
var envJs string

func init() {
	// Render and cache
	envJs = strings.NewReplacer(
		"${OIDC_ISSUER_URL}", os.Getenv("OIDC_ISSUER_URL"),
		"${OIDC_CLIENT_ID}", os.Getenv("OIDC_CLIENT_ID"),
		"${OIDC_SCOPE}", os.Getenv("OIDC_SCOPE"),
	).Replace(envJsTemplate)
}

// envJsHandler serves the env.js file with the environment variables
// substituted at runtime.
func EnvJsHandler(w http.ResponseWriter, r *http.Request) {
	// This handler needs a bit of explanation.  It is solving two problems:
	// 1. The `env.js` file needs to be populated with environment variables
	//    at runtime, not build time, so we can use different values in different
	//    environments (e.g., dev, staging, prod).  Most of these are set in the
	//    init() function, but the `OIDC_ISSUER_URL` poses a problem.
	//    When running in the cloud, the Keycloak server's URL must pass through
	//    unaltered, but development mode is a bit more complicated.
	// 2. When in development mode, the "browser" may be the developer's browser
	//    running on localhost, or it may be a selenium browser running in a
	//    container.  For the developer's browser, we want to use the
	//    localhost URL for the Keycloak server, but for the selenium browser,
	//    we want to use the container URL.  In short, the user's browser can
	//    recognize "localhost:8091", but not "keycloak:8080".  Conversely, the
	//    selenium browser *cannot* recognize "localhost:8091", and it *must*
	//    use "keycloak:8080".  Moreover, because development and testing happen
	//    on the same container, then we must come up with a way to dynamically
	//    set the keycloak URL, depending on who is asking for it.
	// This solution relies on the Angular dev server being configured to not
	// change the origin, which is set in the `.ui/proxy.conf.json` file.  If the
	// origin request is to `localhost:4200`, then this means that the request is
	// being made from the developer's browser and we need to change the keycloak
	// URL to `localhost:8091`.  This change will only happen if this container's
	// `OIDC_ISSUER_URL` environment variable is `http://keycloak:8080/...`
	// in the first place.  In other words, it won't affect a production
	// deployment.
	// In short, this function was written in this way to be a branchless
	// solution to the problem of dynamically setting the Keycloak URL
	// depending on the environment and the browser making the request.
	w.Header().Set("Content-Type", "application/javascript")
	w.Write([]byte(strings.ReplaceAll(envJs, "http://keycloak:8080/", map[bool]string{
		true:  "http://localhost:8091/",
		false: "http://keycloak:8080/",
	}[strings.HasPrefix(r.Host, "localhost:4200")])))
}
