package organization

import (
	"net/http"

	RestOrganization "synapse-its.com/shared/rest/onramp/organization"

	"github.com/gorilla/mux"
)

type Handler struct{}

func NewHandler() *Handler {
	return &Handler{}
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
	router.HandleFunc("/organizations", RestOrganization.CreateHandler).Methods(http.MethodPost)
	router.HandleFunc("/organizations", RestOrganization.GetAllHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{identifier}", RestOrganization.GetByIdentifierHandler).Methods(http.MethodGet)
	router.HandleFunc("/organizations/{identifier}", RestOrganization.UpdateHandler).Methods(http.MethodPatch)
	router.HandleFunc("/organizations/{identifier}", RestOrganization.DeleteHandler).Methods(http.MethodDelete)
}
