package auth

import (
	"context"
	"database/sql"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/onramp/domain"
	mocks "synapse-its.com/shared/mocks"
)

func TestNewPostgresAuthRepository(t *testing.T) {
	t.<PERSON>()

	// Arrange
	fakeDB := &mocks.FakeDBExecutor{}

	// Act
	repo := NewPostgresAuthRepository(fakeDB)

	// Assert
	assert.NotNil(t, repo)
	assert.Implements(t, (*domain.AuthRepository)(nil), repo)
}

func TestStorage_GetByUsername(t *testing.T) {
	t.<PERSON>l()

	tests := []struct {
		name          string
		username      string
		setupDB       func(*mocks.FakeDBExecutor)
		expectedUser  *domain.User
		expectedAuth  *domain.AuthMethod
		expectedError error
	}{
		{
			name:     "successful retrieval",
			username: "testuser",
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					userAuth := dest.(*UserAuthMethod)
					*userAuth = createTestUserAuthMethod()
					return nil
				}
			},
			expectedUser: &domain.User{
				ID:           uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
				FirstName:    "John",
				LastName:     "Doe",
				Mobile:       "+1234567890",
				IanaTimezone: "America/Chicago",
				Description:  "Test user",
			},
			expectedAuth: &domain.AuthMethod{
				ID:           uuid.MustParse("*************-4321-4321-cba987654321"),
				UserID:       uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
				Type:         domain.AuthMethodTypeUsernamePassword,
				Sub:          "",
				Issuer:       "",
				UserName:     "testuser",
				PasswordHash: "hashed_password",
				Email:        "<EMAIL>",
				Metadata: map[string]interface{}{
					"key": "value",
				},
				IsEnabled: true,
			},
			expectedError: nil,
		},
		{
			name:     "user not found",
			username: "nonexistent",
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectedUser:  nil,
			expectedAuth:  nil,
			expectedError: nil,
		},
		{
			name:     "database error",
			username: "testuser",
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection error")
				}
			},
			expectedUser:  nil,
			expectedAuth:  nil,
			expectedError: errors.New("database connection error"),
		},
		{
			name:     "metadata parsing error",
			username: "testuser",
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					userAuth := dest.(*UserAuthMethod)
					*userAuth = createTestUserAuthMethod()
					userAuth.Metadata = []byte(`invalid json`) // Invalid JSON
					return nil
				}
			},
			expectedUser: &domain.User{
				ID:           uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
				FirstName:    "John",
				LastName:     "Doe",
				Mobile:       "+1234567890",
				IanaTimezone: "America/Chicago",
				Description:  "Test user",
			},
			expectedAuth: &domain.AuthMethod{
				ID:           uuid.MustParse("*************-4321-4321-cba987654321"),
				UserID:       uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
				Type:         domain.AuthMethodTypeUsernamePassword,
				Sub:          "",
				Issuer:       "",
				UserName:     "testuser",
				PasswordHash: "hashed_password",
				Email:        "<EMAIL>",
				Metadata:     make(map[string]interface{}), // Should be empty map on parsing error
				IsEnabled:    true,
			},
			expectedError: nil,
		},
		{
			name:     "null metadata",
			username: "testuser",
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					userAuth := dest.(*UserAuthMethod)
					*userAuth = createTestUserAuthMethod()
					userAuth.Metadata = nil // Null metadata
					return nil
				}
			},
			expectedUser: &domain.User{
				ID:           uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
				FirstName:    "John",
				LastName:     "Doe",
				Mobile:       "+1234567890",
				IanaTimezone: "America/Chicago",
				Description:  "Test user",
			},
			expectedAuth: &domain.AuthMethod{
				ID:           uuid.MustParse("*************-4321-4321-cba987654321"),
				UserID:       uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
				Type:         domain.AuthMethodTypeUsernamePassword,
				Sub:          "",
				Issuer:       "",
				UserName:     "testuser",
				PasswordHash: "hashed_password",
				Email:        "<EMAIL>",
				Metadata:     make(map[string]interface{}), // Should be empty map for null metadata
				IsEnabled:    true,
			},
			expectedError: nil,
		},
		{
			name:     "null string fields",
			username: "testuser",
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					userAuth := dest.(*UserAuthMethod)
					*userAuth = createTestUserAuthMethod()
					userAuth.FirstName = nil
					userAuth.LastName = nil
					userAuth.Mobile = nil
					userAuth.Description = nil
					userAuth.Sub = nil
					userAuth.Issuer = nil
					userAuth.UserName = nil
					userAuth.PasswordHash = nil
					userAuth.Email = nil
					return nil
				}
			},
			expectedUser: &domain.User{
				ID:           uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
				FirstName:    "",
				LastName:     "",
				Mobile:       "",
				IanaTimezone: "America/Chicago",
				Description:  "",
			},
			expectedAuth: &domain.AuthMethod{
				ID:           uuid.MustParse("*************-4321-4321-cba987654321"),
				UserID:       uuid.MustParse("12345678-1234-1234-1234-123456789abc"),
				Type:         domain.AuthMethodTypeUsernamePassword,
				Sub:          "",
				Issuer:       "",
				UserName:     "",
				PasswordHash: "",
				Email:        "",
				Metadata: map[string]interface{}{
					"key": "value",
				},
				IsEnabled: true,
			},
			expectedError: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			fakeDB := &mocks.FakeDBExecutor{}
			if tt.setupDB != nil {
				tt.setupDB(fakeDB)
			}

			storage := NewPostgresAuthRepository(fakeDB)
			ctx := context.Background()

			// Act
			user, authMethod, err := storage.GetByUsername(ctx, tt.username)

			// Assert
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
				assert.Nil(t, user)
				assert.Nil(t, authMethod)
			} else {
				assert.NoError(t, err)

				if tt.expectedUser == nil {
					assert.Nil(t, user)
					assert.Nil(t, authMethod)
				} else {
					assert.NotNil(t, user)
					assert.NotNil(t, authMethod)
					assert.Equal(t, tt.expectedUser.ID, user.ID)
					assert.Equal(t, tt.expectedUser.FirstName, user.FirstName)
					assert.Equal(t, tt.expectedUser.LastName, user.LastName)
					assert.Equal(t, tt.expectedUser.Mobile, user.Mobile)
					assert.Equal(t, tt.expectedUser.IanaTimezone, user.IanaTimezone)
					assert.Equal(t, tt.expectedUser.Description, user.Description)

					assert.Equal(t, tt.expectedAuth.ID, authMethod.ID)
					assert.Equal(t, tt.expectedAuth.UserID, authMethod.UserID)
					assert.Equal(t, tt.expectedAuth.Type, authMethod.Type)
					assert.Equal(t, tt.expectedAuth.Sub, authMethod.Sub)
					assert.Equal(t, tt.expectedAuth.Issuer, authMethod.Issuer)
					assert.Equal(t, tt.expectedAuth.UserName, authMethod.UserName)
					assert.Equal(t, tt.expectedAuth.PasswordHash, authMethod.PasswordHash)
					assert.Equal(t, tt.expectedAuth.Email, authMethod.Email)
					assert.Equal(t, tt.expectedAuth.IsEnabled, authMethod.IsEnabled)
					assert.Equal(t, tt.expectedAuth.Metadata, authMethod.Metadata)
				}
			}
		})
	}
}

func TestStorage_UpdateLastLogin(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		user          *domain.User
		authMethod    *domain.AuthMethod
		setupDB       func(*mocks.FakeDBExecutor)
		expectedError error
	}{
		{
			name:       "successful update",
			user:       createTestUser(),
			authMethod: createTestAuthMethod(createTestUser().ID),
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &fakeSQLResult{}, nil
				}
			},
			expectedError: nil,
		},
		{
			name:       "user update error",
			user:       createTestUser(),
			authMethod: createTestAuthMethod(createTestUser().ID),
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					// First call (user update) succeeds, second call (auth method update) fails
					if db.ExecCallCount == 1 {
						return &fakeSQLResult{}, nil
					}
					return nil, errors.New("auth method update error")
				}
			},
			expectedError: errors.New("auth method update error"),
		},
		{
			name:       "auth method update error",
			user:       createTestUser(),
			authMethod: createTestAuthMethod(createTestUser().ID),
			setupDB: func(db *mocks.FakeDBExecutor) {
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database error")
				}
			},
			expectedError: errors.New("database error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			fakeDB := &mocks.FakeDBExecutor{}
			if tt.setupDB != nil {
				tt.setupDB(fakeDB)
			}

			storage := NewPostgresAuthRepository(fakeDB)
			ctx := context.Background()

			// Act
			err := storage.UpdateLastLogin(ctx, tt.user, tt.authMethod)

			// Assert
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				// Verify that Exec was called twice (once for user, once for auth method)
				assert.Equal(t, 2, fakeDB.ExecCallCount)
			}
		})
	}
}

func TestStorage_CreateBasicAuthUser(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		user          *domain.User
		authMethod    *domain.AuthMethod
		setupDB       func(*mocks.FakeDBExecutor)
		expectedError error
	}{
		{
			name:       "successful creation",
			user:       createTestUser(),
			authMethod: createTestAuthMethod(createTestUser().ID),
			setupDB: func(db *mocks.FakeDBExecutor) {
				// First call uses QueryRow (user creation with RETURNING Id)
				db.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"id": "12345678-1234-1234-1234-123456789abc",
					}, nil
				}
				// Second call uses Exec (auth method creation)
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &fakeSQLResult{}, nil
				}
			},
			expectedError: nil,
		},
		{
			name:       "user creation error",
			user:       createTestUser(),
			authMethod: createTestAuthMethod(createTestUser().ID),
			setupDB: func(db *mocks.FakeDBExecutor) {
				// User creation fails (using QueryRow now)
				db.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return nil, errors.New("user creation error")
				}
			},
			expectedError: errors.New("user creation error"),
		},
		{
			name:       "auth method creation error",
			user:       createTestUser(),
			authMethod: createTestAuthMethod(createTestUser().ID),
			setupDB: func(db *mocks.FakeDBExecutor) {
				// User creation succeeds (using QueryRow)
				db.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{
						"id": "12345678-1234-1234-1234-123456789abc",
					}, nil
				}
				// Auth method creation fails (using Exec)
				db.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("auth method creation error")
				}
			},
			expectedError: errors.New("auth method creation error"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Arrange
			fakeDB := &mocks.FakeDBExecutor{}
			if tt.setupDB != nil {
				tt.setupDB(fakeDB)
			}

			storage := NewPostgresAuthRepository(fakeDB)
			ctx := context.Background()

			// Act
			err := storage.CreateBasicAuthUser(ctx, tt.user, tt.authMethod)

			// Assert
			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedError.Error(), err.Error())
			} else {
				assert.NoError(t, err)
				// Verify that QueryRow was called once (user creation) and Exec was called once (auth method creation)
				assert.Equal(t, 1, fakeDB.QueryRowCallCount)
				assert.Equal(t, 1, fakeDB.ExecCallCount)
			}
		})
	}
}

func TestStringValue(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		input    *string
		expected string
	}{
		{
			name:     "nil pointer",
			input:    nil,
			expected: "",
		},
		{
			name:     "empty string",
			input:    stringPtr(""),
			expected: "",
		},
		{
			name:     "non-empty string",
			input:    stringPtr("test"),
			expected: "test",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Act
			result := stringValue(tt.input)

			// Assert
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Helper function to create string pointers
func stringPtr(s string) *string {
	return &s
}

// Fake SQL result for testing
type fakeSQLResult struct{}

func (f *fakeSQLResult) LastInsertId() (int64, error) {
	return 1, nil
}

func (f *fakeSQLResult) RowsAffected() (int64, error) {
	return 1, nil
}
