package app

import (
	"context"
	"strings"
	"time"

	"github.com/coreos/go-oidc"
	"github.com/gorilla/mux"
	"golang.org/x/oauth2"

	"synapse-its.com/onramp/assets"
	"synapse-its.com/onramp/modules/auth"
	"synapse-its.com/shared/api/middleware"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/httplogger"
	"synapse-its.com/shared/logger"
)

// NewRouter initializes the router and registers routes.
func NewRouter(connections *connect.Connections, batch bqbatch.Batcher) *mux.Router {
	router := mux.NewRouter()

	// Apply middleware globally (e.g., logging, authentication).
	router.Use(middleware.ConnectionsMiddleware(connections))
	router.Use(middleware.BQBatchMiddleware(batch))
	router.Use(httplogger.LoggingMiddleware)

	// Define default endpoints
	router.HandleFunc("/assets/env.js", assets.EnvJsHandler)

	// Note: Protected routes are set up in App.Serve() where we have access to the auth handler's session store

	return router
}

// Initialize all OIDC configurations.
func init() {
	ctx := context.Background()

	// Real Synapse OIDC configuration
	var err error
	start := time.Now()
	for {
		auth.SynapseOIDC.Provider, err = oidc.NewProvider(ctx, auth.SynapseOIDC.IssuerURL)
		if err != nil {
			logger.Warnf("failed to init real OIDC provider (OK for testing, bad for prod): %v", err)
			time.Sleep(time.Second)
			continue
		}
		break
	}
	logger.Info(time.Since(start))

	auth.SynapseOIDC.Verifier = auth.SynapseOIDC.Provider.Verifier(&oidc.Config{
		ClientID: auth.SynapseOIDC.ClientID,
	})
	auth.SynapseOIDC.OAuth2Config = &oauth2.Config{
		ClientID:     auth.SynapseOIDC.ClientID,
		ClientSecret: auth.SynapseOIDC.ClientSecret,
		Endpoint:     auth.SynapseOIDC.Provider.Endpoint(),
		RedirectURL:  auth.SynapseOIDC.RedirectURL,
		Scopes:       auth.SynapseOIDCScopes,
	}

	// Local Synapse OIDC configuration for development
	auth.SynapseOIDCLocal = auth.SynapseOIDC

	// Override only the URLs for dev.  In production, the URLs won't contain
	// "onramp" or "keycloak", so they won't change.
	auth.SynapseOIDCLocal.IssuerURL = strings.ReplaceAll(
		auth.SynapseOIDC.IssuerURL,
		"keycloak:8080",
		"localhost:8091",
	)
	auth.SynapseOIDCLocal.RedirectURL = strings.ReplaceAll(
		auth.SynapseOIDC.RedirectURL,
		"onramp:4200",
		"localhost:4200",
	)

	// Re-init provider/verifier/oauth2.Config for localhost
	auth.SynapseOIDCLocal.Provider, err = oidc.NewProvider(oidc.ClientContext(ctx, auth.LocalhostHTTPProxy), auth.SynapseOIDCLocal.IssuerURL)
	if err != nil {
		// In production, this will error because there is no OIDC provider
		// listening on localhost:8091.  `synapseOIDCLocal` is a copy of the actual
		// OIDC provider because it is not overwritten here.  If someone tries to
		// send us forged host headers, the worst that will happen is that nothing
		// will validate, which is what we want.
		logger.Warnf("failed to init local OIDC provider: %v", err)
	} else {
		// If we made it here, then the provider was able to talk to the OIDC
		// server on localhost.
		auth.SynapseOIDCLocal.Verifier = auth.SynapseOIDCLocal.Provider.Verifier(&oidc.Config{
			ClientID: auth.SynapseOIDCLocal.ClientID,
		})
		auth.SynapseOIDCLocal.OAuth2Config = &oauth2.Config{
			ClientID:     auth.SynapseOIDCLocal.ClientID,
			ClientSecret: auth.SynapseOIDCLocal.ClientSecret,
			Endpoint:     auth.SynapseOIDCLocal.Provider.Endpoint(),
			RedirectURL:  auth.SynapseOIDCLocal.RedirectURL,
			Scopes:       auth.SynapseOIDCScopes,
		}
	}
}
