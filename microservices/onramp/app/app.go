package app

import (
	"encoding/json"
	"net/http"
	"os"

	"github.com/gorilla/mux"
	"synapse-its.com/onramp/domain"
	"synapse-its.com/onramp/handlers"
	"synapse-its.com/onramp/middlewares"
	"synapse-its.com/onramp/modules/auth"
	"synapse-its.com/onramp/modules/organization"
	"synapse-its.com/onramp/modules/softwaregateway"
	"synapse-its.com/onramp/modules/user"
	"synapse-its.com/onramp/pkg"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// UserProfile represents the user profile response
type UserProfile struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

type App struct {
	// TODO: This is a hack to get the router working.  We need to refactor this
	muxRouter *mux.Router
	// Application-specific handlers
	authHandler            *auth.Handler
	organizationHandler    *organization.Handler
	softwareGatewayHandler *softwaregateway.Handler
	userHandler            *user.Handler
}

func NewApp(connections *connect.Connections, batch bqbatch.Batcher) *App {
	authService := auth.NewService(auth.NewPostgresAuthRepository(connections.Postgres), pkg.NewPasswordHasher(), pkg.NewTokenGenerator())
	authHandler := auth.NewHandler(authService)
	organizationHandler := organization.NewHandler()
	softwareGatewayHandler := softwaregateway.NewHandler()
	userHandler := user.NewHandler(authHandler.GetSessionStore())
	return &App{
		muxRouter:              NewRouter(connections, batch),
		authHandler:            authHandler,
		organizationHandler:    organizationHandler,
		softwareGatewayHandler: softwareGatewayHandler,
		userHandler:            userHandler,
	}
}

func (a *App) Serve() *mux.Router {
	router := a.muxRouter

	// Set up protected routes with proper session store injection
	protectedRouter := router.PathPrefix("/protected").Subrouter()
	protectedRouter.Use(middlewares.AuthMiddleware(a.authHandler.GetSessionStore()))
	protectedRouter.HandleFunc("/foo", handlers.FooHandler).Methods(http.MethodGet)
	protectedRouter.HandleFunc("/profile", func(w http.ResponseWriter, r *http.Request) {
		// Fetch the claims from the context.
		claims, ok := r.Context().Value(domain.UserKey).(map[string]interface{})
		if !ok {
			http.Error(w, "unauthorized", http.StatusUnauthorized)
			return
		}

		// Extract the name and email from the claims.
		name, okName := claims["name"].(string)
		name = map[bool]string{
			true:  name,
			false: "",
		}[okName]
		email, okEmail := claims["email"].(string)
		email = map[bool]string{
			true:  email,
			false: "",
		}[okEmail]
		profile := UserProfile{
			Name:  name,
			Email: email,
		}

		// If no email address, then we don't return the profile.
		if email == "" {
			http.Error(w, "profile not found", http.StatusNotFound)
			return
		}

		// Return the profile as JSON
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(profile)
	})

	// TODO: Apply the middleware to secure the api subrouter
	apiRouter := router.PathPrefix("/api").Subrouter()
	apiRouter.HandleFunc("/foo", handlers.FooHandler).Methods(http.MethodGet)
	apiRouter.HandleFunc("/gateway", handlers.GatewayHandler).Methods(http.MethodGet)
	apiRouter.HandleFunc("/gateway-config", handlers.ConfigGatewayHandler).Methods(http.MethodGet)
	apiRouter.HandleFunc("/devices", handlers.DevicesHandler).Methods(http.MethodGet)

	a.authHandler.RegisterRoutes(router)
	a.organizationHandler.RegisterRoutes(apiRouter)
	a.softwareGatewayHandler.RegisterRoutes(apiRouter)
	a.userHandler.RegisterRoutes(apiRouter)

	// The catch-all (file system server) for serving the Angular app.
	webContentDir := os.Getenv("WEB_CONTENT_DIR")
	if webContentDir == "" {
		logger.Warn("WEB_CONTENT_DIR is not set, using default /web")
		webContentDir = "/web"
	}
	fs := http.FileServer(http.Dir(webContentDir))
	router.PathPrefix("/").Handler(fs)

	return router
}
